package com.mzj.py.config.mqtt;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Configuration
@Slf4j
public class MqttSubscriptConfig {

    @Value("${mqtt.broker}")
    private String brokerUrl;

    @Value("${mqtt.clientId}")
    private String clientId;

    @Value("${mqtt.username}")
    private String username;

    @Value("${mqtt.password}")
    private String password;

    @Autowired
    private MqttSubscriptCallBack mqttSubscriptCallBack;

    /**
     * 客户端对象
     */
    private MqttAsyncClient client;

    /**
     * 固定的客户端ID，在应用启动时生成一次
     */
    private String fixedClientId;

    /**
     * 在bean初始化后连接到服务器
     */
    @PostConstruct
    public void init() {
        // 初始化固定的客户端ID
        fixedClientId = clientId + "-Sub-" + System.currentTimeMillis();
        connect();
    }

    /**
     * 客户端连接服务端
     */
    public synchronized void connect() {
        // 如果客户端已连接，直接返回
        if (client != null && client.isConnected()) {
            log.info("MQTT订阅客户端已连接，无需重复创建连接");
            return;
        }

        // 如果客户端存在但未连接，先关闭旧连接
        if (client != null) {
            try {
                client.close();
                log.info("关闭旧的MQTT订阅客户端连接");
            } catch (MqttException e) {
                log.warn("关闭旧订阅连接时出现异常: {}", e.getMessage());
            }
        }
        // 连接设置
        MqttConnectOptions options = new MqttConnectOptions();
        // 是否清空session，设置false表示服务器会保留客户端的连接记录（订阅主题，qos）,客户端重连之后能获取到服务器在客户端断开连接期间推送的消息
        // 设置为true表示每次连接服务器都是以新的身份
        options.setCleanSession(false);
        // 设置连接用户名
        options.setUserName(username);
        // 设置连接密码
        options.setPassword(password.toCharArray());
        // 设置超时时间，单位为秒
        options.setConnectionTimeout(60);
        // 设置心跳时间 单位为秒，表示服务器每隔 1.5*10秒的时间向客户端发送心跳判断客户端是否在线
        options.setKeepAliveInterval(20);
        // 开启自动重连
        options.setAutomaticReconnect(true);
        // 设置最大重连时间间隔 (可选)，单位是毫秒，设置为 5000 表示最多等待 5 秒再尝试重连
        options.setMaxReconnectDelay(5000);
        // 设置遗嘱消息的话题，若客户端和服务器之间的连接意外断开，服务器将发布客户端的遗嘱信息
        // 注释掉遗嘱消息，避免不必要的willTopic发布
        // options.setWill("willTopic", (clientId + "-Sub" + "与服务器断开连接").getBytes(), 0,
        // false);
        try {
            // 创建MQTT客户端对象
            // 使用固定的客户端ID，避免每次连接都触发webhook
            client = new MqttAsyncClient(brokerUrl, fixedClientId,
                    new MemoryPersistence());
            // 设置回调
            client.setCallback(mqttSubscriptCallBack);
            // 使用异步连接
            client.connect(options, null, new IMqttActionListener() {
                @Override
                public void onSuccess(IMqttToken asyncActionToken) {
                    log.info("MQTT连接成功");
                    // 连接成功后订阅主题
                    try {
                        // 订阅主题
                        // 消息等级，和主题数组一一对应，服务端将按照指定等级给订阅了主题的客户端推送消息
                        int[] qos = { 2 };
                        String[] topics = { "device/command/response/#" };
                        // 使用异步订阅并添加回调处理
                        client.subscribe(topics, qos, null, new IMqttActionListener() {
                            @Override
                            public void onSuccess(IMqttToken asyncActionToken) {
                                log.info("订阅主题成功: {}", String.join(",", topics));
                            }

                            @Override
                            public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                                log.error("订阅主题失败：{}", exception.getMessage());
                            }
                        });
                    } catch (MqttException e) {
                        log.error("订阅主题异常：" + e.getMessage());
                    }
                }

                @Override
                public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                    log.error("MQTT连接失败：" + exception.getMessage());
                }
            });
        } catch (MqttException e) {
            e.printStackTrace();
            log.error("mqtt连接失败。。" + e.getMessage());
        }
    }

    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return client != null && client.isConnected();
    }

    /**
     * 获取客户端ID
     */
    public String getClientId() {
        return client != null ? client.getClientId() : "未连接";
    }

    /**
     * 等待连接建立 (预留方法，可用于需要等待连接的场景)
     */
    @SuppressWarnings("unused")
    private boolean waitForConnection(long timeoutMs) {
        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            if (client != null && client.isConnected()) {
                log.info("MQTT订阅客户端连接成功");
                return true;
            }
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        log.error("等待MQTT订阅客户端连接超时");
        return false;
    }
}
